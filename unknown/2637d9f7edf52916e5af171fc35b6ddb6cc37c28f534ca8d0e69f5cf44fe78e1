import {
	adminCustom<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	apContactWebhookHandler,
	ccWebhookHandler,
	cfHandler,
	handleErrorCleanup,
} from "@handlers";
import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { requestId } from "hono/request-id";
import { createQueueRoutes } from "@/routes/queueRoutes";
import { logError } from "@/utils/logger";

const app = new Hono<Env>();
app.use(contextStorage());
app.use("*", requestId());

app.onError((err, c) => {
	const requestId = c.get("requestId") || "unknown";
	logError("Unhandled application error", err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId,
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) => c.text("DermaCare Data Sync Service - OK"));

app.get("/cf", cfHandler);

// Webhook endpoints (simplified - return empty JSON)
app.post("/webhooks/ap", (c) => c.json({}));
app.post("/webhooks/cc", (c) => c.json({}));

// Internal webhook endpoints (replicas)
app.post("/internal-webhook/ap", apContactWebhookHandler);
app.post("/internal-webhook/cc", ccWebhookHandler);

// Admin endpoints
app.get("/admin/cleanup-errors", handleErrorCleanup);
app.post(
	"/admin/custom-fields-sync/:id/:platform",
	adminCustomFieldsSyncHandler,
);

// Queue system endpoints
app.route("/api/queue", createQueueRoutes());

export default app;
